import numpy as np
import matplotlib.pyplot as plt
from matplotlib.colors import LinearSegmentedColormap


# 混淆矩阵数据
matrix = np.array([[199, 1],
                   [3, 197]])

# 类别数量
num_speed = 3
num_pressure = 3
# 类别名称
labels = ['Normal', 'loose0.63', 'loose0.81', 'loose1.02']
# 自定义颜色图
# colors = ['#9ebcda','#e0ecf4', '#f7fcfd', '#fdae61', '#fe8011']  # 蓝色到橘色渐变
# colors = ['#9ebcda', '#f7fcfd', '#fe8011']  # 蓝色到橘色渐变
# colors = ['#97d2f6','#e0ecf4', '#f7fcfd', '#fdae61', '#fe8011']  # 蓝色到橘色渐变
# colors = ['#97d2f6', '#f7fcfd',  '#fe8011']  # 蓝色到橘色渐变
# colors = ['#97d2f6','#ffffff']  # 白色到蓝色
colors = ['#f7fbff','#9ebcda']  # 白色到蓝色

cmap = LinearSegmentedColormap.from_list('CustomColors', colors)

# 自定义字体
font = {'family': 'Times New Roman',
        'weight': 'normal',
        'size': 12}

# 绘制混淆矩阵图
# plt.imshow(matrix, cmap=plt.cm.Blues)
plt.imshow(matrix, cmap=cmap)

# 绘制网格
plt.xticks(np.arange(matrix.shape[1]+1)-.5, minor=True)
plt.yticks(np.arange(matrix.shape[0]+1)-.5, minor=True)
plt.grid(which='minor', color='white', linestyle='-', linewidth=0.6)
plt.tick_params(which="minor", bottom=False, left=False)

plt.legend(frameon=False)

# plt.colorbar()
cb = plt.colorbar(cmap=cmap)
cb.ax.set_yticklabels(cb.ax.get_yticklabels(), fontsize=12, fontname='Times New Roman')  # 设置colorbar的字体样式和大小

# plt.tight_layout()  # 使图形显示更加紧凑，否则信息可能被遮挡
# plt.savefig("./fig/heatmap_horizontal_bar.tiff", dpi=1600)
# plt.show()


